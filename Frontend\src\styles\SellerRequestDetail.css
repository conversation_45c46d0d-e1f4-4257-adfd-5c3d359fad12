/* Seller Request Detail Styles - Following consistent dashboard pattern */
.request-detail-container {

  background: var(--white);
  border-radius: var(--border-radius-large);
 
 
}

/* Header - Following consistent header pattern */

.back-btn {
  display: flex;
  align-items: center;
  gap: var(--border-radius-medium);
  padding: 10px var(--basefont);
  background: var(--bg-gray);
  border: none;
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: var(--primary-light-color);
  color: var(--primary-color);
  transform: scale(1.02);
}

.header-actions {
  display: flex;
  gap: var(--extrasmallfont);
}

/* Request Overview - Following consistent overview pattern */
.request-overview {
  margin-bottom: var(--heading4);
}

.overview-header {
  margin-bottom: var(--heading5);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.request-title-section {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--smallfont);
}

.request-title {
  margin: 0;
  font-size: var(--heading3);
  color: var(--secondary-color);
  font-weight: 700;
}

.request-meta {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.request-id {
  font-family: monospace;
  background: var(--bg-gray);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  font-weight: 500;
}

.request-date {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--heading5);
}

.overview-card {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--basefont);
  background: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
}

.card-icon {
  font-size: var(--heading6);
  color: var(--primary-color);
}

.card-header h3 {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 600;
}

.card-content {
  padding: var(--basefont);
}

/* Buyer Details - Following consistent info display pattern */
.buyer-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.buyer-name {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
}

.buyer-email {
  font-size: var(--basefont);
  color: var(--dark-gray);
}

/* Budget Details - Following consistent data display pattern */
.budget-details,
.request-details {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.budget-item,
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--extrasmallfont) 0;
  border-bottom: 1px solid var(--bg-gray);
}

.budget-item:last-child,
.detail-item:last-child {
  border-bottom: none;
}

.budget-item .label,
.detail-item .label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

.budget-item .value,
.detail-item .value {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
}

.budget-item .value.completed {
  color: #2ecc71;
  font-weight: 700;
}

.budget-item .value.pending {
  color: #f39c12;
  font-weight: 700;
}

/* Status Badge - Following consistent badge pattern */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: capitalize;
}

.status-orange {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.status-green {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.status-red {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.status-blue {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.status-purple {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.status-gray {
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Request Sections - Following consistent section pattern */
.request-section {
  margin-bottom: var(--heading4);
  padding: var(--heading5);
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.request-section:hover {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.section-title {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 600;
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--extrasmallfont);
}

.description-content {
  background: var(--bg-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
  margin-top: var(--basefont);
}

.description-content p {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  line-height: 1.6;
}

/* Response Content - Following consistent content pattern */
.response-content,
.submission-content {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.response-status,
.submission-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--extrasmallfont) 0;
  border-bottom: 1px solid var(--light-gray);
}

.response-badge,
.submission-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
}

.response-badge.accepted,
.submission-badge {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.response-badge.rejected {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.response-date,
.submission-date {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.response-message,
.submission-message {
  background: var(--bg-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-color);
}

.response-message h4,
.submission-message h4 {
  margin: 0 0 var(--extrasmallfont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 600;
}

.response-message p,
.submission-message p {
  margin: 0;
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.5;
}

.submitted-content {
  display: flex;
  justify-content: flex-start;
  margin-top: var(--basefont);
}

/* Empty State - Following consistent empty state pattern */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
  background: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

.empty-state h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 var(--heading5) 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
  max-width: 400px;
  line-height: 1.5;
}

/* Responsive Design - Following consistent responsive pattern */
@media (max-width: 1024px) {
  .overview-grid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }


}

@media (max-width: 768px) {




  .header-actions {
    justify-content: center;
  }

  .request-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .request-title {
    font-size: var(--heading4);
  }

  .request-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .budget-item,
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
    padding: var(--smallfont) 0;
  }

  .response-status,
  .submission-status {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .request-section {
    padding: var(--basefont);
  }

  .card-content {
    padding: var(--smallfont);
  }
}

@media (max-width: 480px) {


  .back-btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--smallfont);
  }

  .request-title {
    font-size: var(--heading5);
  }

  .overview-grid {
    gap: var(--smallfont);
  }
}
