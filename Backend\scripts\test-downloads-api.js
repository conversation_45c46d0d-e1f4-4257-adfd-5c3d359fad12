const mongoose = require('mongoose');
require('dotenv').config();

const testDownloadsAPI = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const User = require('../models/User');
    
    // Import the controller function directly
    const { getBuyerDownloads } = require('../controllers/orders');

    // Find the buyer
    const buyer = await User.findOne({ email: '<EMAIL>' });
    
    if (!buyer) {
      console.log('❌ Buyer not found.');
      return;
    }

    console.log(`🔍 Testing downloads API for buyer: ${buyer.firstName} ${buyer.lastName}`);
    console.log(`👤 Buyer ID: ${buyer._id}`);

    // Mock request and response objects
    const req = {
      user: { id: buyer._id.toString() },
      query: { page: 1, limit: 10 }
    };

    let responseData = null;
    let responseStatus = null;

    const res = {
      status: (code) => {
        responseStatus = code;
        return {
          json: (data) => {
            responseData = data;
            return res;
          }
        };
      }
    };

    const next = (error) => {
      console.error('❌ API Error:', error);
    };

    console.log('\n🔍 Calling getBuyerDownloads API...');
    
    // Call the API function
    await getBuyerDownloads(req, res, next);

    console.log(`\n📊 API Response Status: ${responseStatus}`);
    console.log(`📦 API Response Data:`);
    console.log(JSON.stringify(responseData, null, 2));

    if (responseData?.success && responseData?.data?.downloads) {
      console.log(`\n📋 Downloads returned by API: ${responseData.data.downloads.length}`);
      
      responseData.data.downloads.forEach((download, index) => {
        console.log(`\n${index + 1}. Download:`);
        console.log(`   - ID: ${download._id}`);
        console.log(`   - Order ID: ${download.orderId}`);
        console.log(`   - Title: ${download.title}`);
        console.log(`   - Coach: ${download.coach}`);
        console.log(`   - File Type: ${download.fileType}`);
        console.log(`   - Is Custom Content: ${download.isCustomContent}`);
        console.log(`   - Purchase Date: ${download.purchaseDate}`);
        console.log(`   - Amount: $${download.amount}`);
      });
    }

  } catch (error) {
    console.error('💥 Error testing downloads API:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n📡 MongoDB connection closed');
  }
};

// Run the test
testDownloadsAPI();
