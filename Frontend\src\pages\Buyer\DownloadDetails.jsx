import { useEffect, useState, useRef } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  selectMyDownloads,
  selectMyCustomContent,
  fetchBuyerDownloads,
  fetchBuyerCustomContent,
} from "../../redux/slices/buyerDashboardSlice";
import BuyerSidebar from "../../components/buyer/BuyerSidebar";
import SimplePDFViewer from "../../components/common/SimplePDFViewer";
import DocumentViewer from "../../components/common/DocumentViewer";
import ReviewModal from "../../components/common/ReviewModal";
import PreviewModal from "../../components/common/PreviewModal";
import { IoArrowBack } from "react-icons/io5";
import {
  FaPlay,
  FaDownload,
  FaSync,
  FaFilePdf,
  FaFileAudio,
  FaFileImage,
  FaFile,
  FaStar,
  FaStarHalf,
} from "react-icons/fa";
import { MdVisibility } from "react-icons/md";
import { toast } from "react-toastify";
import {
  getSmartFileUrl,
  getProxyContentUrl,
  getProxyStreamUrl,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth,
  isS3Url,
} from "../../utils/constants";
import { generateFilename } from "../../utils/downloadUtils";
import { formatStandardDate } from "../../utils/dateValidation";
import api from "../../services/api";
import "../../styles/BuyerAccount.css";
import "../../styles/DownloadDetails.css";
import DynamicHeading from "../../components/common/DynamicHeading";
import { AiOutlineArrowLeft } from "react-icons/ai";
import RatingStars from "../../components/common/RatingStars";

const getDownloadDetails = (downloadId, downloads, customContent) => {
  // First check regular downloads
  const foundDownload = downloads.find(
    (download) => download._id === downloadId || download.id === downloadId
  );

  if (foundDownload) {
    return {
      ...foundDownload,
      orderId: `#${downloadId}245578`,
      date: foundDownload.downloadDate || "30 May 2024",
      time: "4:30PM",
      items: 1,
      totalAmount: "$22.00",
      customer: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "+1 *********",
      },
      payment: {
        method: "Mastercard",
        cardNumber: "**** **** **** 1234",
      },
      videoUrl:
        "https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=800&h=450&auto=format&fit=crop",
      description:
        "Learn Vortex - Drills and Coaching Philosophies to Developing Toughness in your Players to Win on the Court and in Life. Coach Vortex has been coaching for over 20 years and some advice for High School Coaches. Coach Vortex speaks on what does the building the Culture/Mindset that his Teams and Coaches for. Coach Vortex also does advice for College Coaches, where knowing him as favorite Drills when help teach a Game speed for toughness.",
      isCustomContent: false,
    };
  }

  // Then check custom content
  const foundCustomContent = customContent.find(
    (content) => content._id === downloadId || content.id === downloadId
  );

  if (foundCustomContent) {
    return {
      ...foundCustomContent,
      orderId: `#${downloadId}245578`,
      date: foundCustomContent.completedAt || "30 May 2024",
      time: "4:30PM",
      items: 1,
      totalAmount: "$22.00",
      customer: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "+1 *********",
      },
      payment: {
        method: "Mastercard",
        cardNumber: "**** **** **** 1234",
      },
      videoUrl:
        "https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=800&h=450&auto=format&fit=crop",
      description: foundCustomContent.description || "Custom content created specifically for you.",
      isCustomContent: true,
      // Map custom content fields to download format
      downloadDate: foundCustomContent.completedAt,
      fileType: foundCustomContent.contentType,
      fileUrl: foundCustomContent.fileUrl,
      content: {
        _id: foundCustomContent._id,
        title: foundCustomContent.title,
        description: foundCustomContent.description,
        contentType: foundCustomContent.contentType,
        fileUrl: foundCustomContent.fileUrl,
        thumbnailUrl: foundCustomContent.thumbnailUrl,
        strategicContent: foundCustomContent.strategicContent,
      },
    };
  }

  return null;
};

const DownloadDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const customContent = useSelector(selectMyCustomContent);
  const { user } = useSelector((state) => state.auth);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [userReview, setUserReview] = useState(null);
  const [contentReviews, setContentReviews] = useState([]);
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [signedFileUrl, setSignedFileUrl] = useState(null);
  const [isLoadingSignedUrl, setIsLoadingSignedUrl] = useState(false);
  const [localAverageRating, setLocalAverageRating] = useState(0);


  const [showDebugInfo, setShowDebugInfo] = useState(false);

  // Video player ref
  const videoRef = useRef(null);

  const download = getDownloadDetails(id, downloads, customContent);



  useEffect(() => {
    if (!downloads.length) {
      dispatch(fetchBuyerDownloads());
    }
    if (!customContent.length) {
      dispatch(fetchBuyerCustomContent());
    }
  }, [dispatch, downloads.length, customContent.length]);

  useEffect(() => {
    if (!download) {
      dispatch(fetchBuyerDownloads());
      dispatch(fetchBuyerCustomContent());
    }
  }, [id, dispatch]);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        const response = await api.get(
          `/reviews/content/${download.content._id}`
        );
        setContentReviews(response.data.data);
        // Find user's review if exists
        const foundUserReview = response.data.data.find(
          (review) => review.user._id === user._id
        );
        setUserReview(foundUserReview || null);
        setLoadingReviews(false);
      } catch (error) {
        console.error("Error fetching reviews:", error);
        setLoadingReviews(false);
      }
    };

    if (download?.content?._id) {
      fetchReviews();
    }
  }, [download?.content?._id, user._id]);

  // Set file URL - use proxy URLs for secure access
  useEffect(() => {
    if (download?.content?._id) {
      // Use proxy URL for secure content access
      const proxyUrl = getProxyContentUrl(download.content._id);
      setSignedFileUrl(proxyUrl);
      setIsLoadingSignedUrl(false);
    } else if (download?.fileUrl) {
      // Fallback to original URL for local files
      setSignedFileUrl(download.fileUrl);
      setIsLoadingSignedUrl(false);
    }
  }, [download?.content?._id, download?.fileUrl]);

  // Update video src when proxy URL is ready
  useEffect(() => {
    if (signedFileUrl && videoRef.current) {
      // For video files, use streaming proxy URL for better performance
      if (
        download?.fileType?.toLowerCase() === "video" ||
        download?.content?.contentType?.toLowerCase() === "video" ||
        (download?.fileUrl &&
          /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(download.fileUrl))
      ) {
        const streamUrl = download?.content?._id
          ? getProxyUrlWithAuth(getProxyStreamUrl(download.content._id))
          : signedFileUrl;
        videoRef.current.src = streamUrl;
        videoRef.current.load(); // Force reload the video
      }
    }
  }, [
    signedFileUrl,
    download?.fileType,
    download?.content?.contentType,
    download?.content?._id,
    download?.fileUrl,
  ]);

  useEffect(() => {
    if (download?.content?.averageRating) {
      setLocalAverageRating(download.content.averageRating);
    }
  }, [download?.content?.averageRating]);

  const getFileIcon = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case "video":
        return <FaPlay className="file-icon video" />;
      case "pdf":
      case "document":
        return <FaFilePdf className="file-icon pdf" />;
      case "audio":
        return <FaFileAudio className="file-icon audio" />;
      case "image":
        return <FaFileImage className="file-icon image" />;
      default:
        return <FaFile className="file-icon default" />;
    }
  };

  // Show loading state while downloads are being fetched
  if (!downloads.length) {
    return (
      <div className="BuyerAccount">
        <div className="container max-container">
          <div className="sidebar">
            <BuyerSidebar />
          </div>
          <div className="contentArea">
            <div className="bordrdiv mb-30">
              <DynamicHeading
                title="Download Details"
                onBack={() => navigate("/buyer/account/downloads")}
                backIcon={<AiOutlineArrowLeft style={{ fontSize: 20 }} />}
                backLabel="Back"
              />
            </div>
            <div className="DownloadDetails__error">
              <p>Loading download details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error if download not found after downloads are loaded
  if (!download) {
    return (
      <div className="BuyerAccount">
        <div className="container max-container">
          <div className="sidebar">
            <BuyerSidebar />
          </div>
          <div className="contentArea">
            <div className="bordrdiv mb-30">
              <DynamicHeading
                title="Download Details"
                onBack={() => navigate("/buyer/account/downloads")}
                backIcon={<AiOutlineArrowLeft style={{ fontSize: 20 }} />}
                backLabel="Back"
              />
            </div>
            <div className="DownloadDetails__error">
              <p>Content not found. ID: {id}</p>
              <p>This content may not exist in your downloads or custom content.</p>
              <button onClick={() => navigate("/buyer/account/downloads")}>
                Back to Downloads
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleDownload = async () => {
    try {
      // Check if this is custom content
      if (download.isCustomContent && download.fileUrl) {
        const { downloadFileFromUrl, generateFilename } = await import('../../utils/downloadUtils');
        const filename = generateFilename(download.title, download.fileType);

        await downloadFileFromUrl(download.fileUrl, filename, {
          isCustomContent: true
        });

        toast.success("Download started successfully!");
      } else {
        toast.error("Download functionality has been disabled for security purposes");
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error(error.message || "Failed to download file");
    }
  };

  const handleReviewSubmitted = (newReview) => {
    if (newReview === null) {
      // Review was deleted
      setContentReviews((prevReviews) =>
        prevReviews.filter((review) => review._id !== userReview._id)
      );
      setUserReview(null);
    } else {
      // Add user information to the review object
      const reviewWithUser = {
        ...newReview,
        user: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          name: user.name || `${user.firstName} ${user.lastName}`,
          username: user.username,
        },
      };

      if (userReview) {
        // Update existing review
        setContentReviews((prevReviews) =>
          prevReviews.map((review) =>
            review._id === newReview._id ? reviewWithUser : review
          )
        );
      } else {
        // Add new review
        setContentReviews((prevReviews) => [...prevReviews, reviewWithUser]);
      }

      // Only update userReview if we're editing an existing review
      if (userReview) {
        setUserReview(reviewWithUser);
      }
    }

    // Update the local average rating
    const updatedReviews =
      newReview === null
        ? contentReviews.filter((review) => review._id !== userReview?._id)
        : userReview
          ? contentReviews.map((review) =>
            review._id === newReview._id ? newReview : review
          )
          : [...contentReviews, newReview];

    const newAverageRating = calculateAverageRating(updatedReviews);
    setLocalAverageRating(newAverageRating);

    // Close the modal
    setIsReviewModalOpen(false);
  };

  // Helper function to calculate average rating
  const calculateAverageRating = (reviews) => {
    if (!reviews.length) return 0;
    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    return sum / reviews.length;
  };

  const handleEditReview = (review) => {
    // Keep track of the review being edited
    setUserReview(review);
    setIsReviewModalOpen(true);
  };

  // Update the ReviewModal close handler
  const handleModalClose = () => {
    // Don't reset userReview state when closing modal
    setIsReviewModalOpen(false);
  };

  const handleDeleteReview = async (reviewId) => {
    try {
      await api.delete(`/reviews/${reviewId}`);
      // Clear the userReview state to show the Write Review button again
      setUserReview(null);
      // Remove the review from the list
      setContentReviews((prevReviews) =>
        prevReviews.filter((r) => r._id !== reviewId)
      );
      toast.success("Review deleted successfully");
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to delete review");
    }
  };

  const purchaseDate = formatStandardDate(download.downloadDate);
  const thumbnailUrl = download?.content?._id
    ? getProxyUrlWithAuth(getProxyThumbnailUrl(download.content._id))
    : download.thumbnailUrl
      ? getSmartFileUrl(download.thumbnailUrl)
      : null;

  return (
    <div className="BuyerAccount">
      <div className="container max-container">
        <div className="sidebar">
          <BuyerSidebar />
        </div>
        <div className="contentArea">
          <div className="bordrdiv mb-30">
            <DynamicHeading
              title="Download Details"
              onBack={() => navigate("/buyer/account/downloads")}
              backIcon={<AiOutlineArrowLeft style={{ fontSize: 20 }} />}
              backLabel="Back"
            />
          </div>
          <div className="DownloadDetails">
            <div className="DownloadDetails__wrapper">
              {/* Header with seller layout pattern */}
              {/* <div className="bordrdiv mb-30">
                <div className="DownloadDetails__header-container">
                  <div className="DownloadDetails__actions">
                 
                    {!loadingReviews && !contentReviews.some(review => review.user._id === user._id) && (
                      <button
                        className="DownloadDetails__review-btn"
                        onClick={() => setIsReviewModalOpen(true)}
                      >
                        Write a Review
                      </button>
                    )}
                  </div>
                </div>
              </div> */}

              {/* Content Info */}
              <div className="DownloadDetails__content-mainone">
                <div className="DownloadDetails__content-info">
                  <div className="DownloadDetails__content-main">
                    <div className="DownloadDetails__content-image">
                      {thumbnailUrl ? (
                        <img
                          src={thumbnailUrl}
                          alt={download.title}
                          onError={(e) => {
                            e.target.style.display = "none";
                            e.target.nextSibling.style.display = "flex";
                          }}
                        />
                      ) : null}
                      <div
                        className="content-placeholder"
                        style={{ display: thumbnailUrl ? "none" : "flex" }}
                      >
                        {getFileIcon(download.fileType)}
                      </div>
                    </div>
                    <div className="DownloadDetails__content-details">
                      <div>
                        <h3 className="DownloadDetails__content-title">
                          {download.title}
                        </h3>
                        <p className="DownloadDetails__content-coach">
                          By {download.coach}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="DownloadDetails__rating">
                      <RatingStars rating={localAverageRating} size={24} />
                      <span>({contentReviews.length} reviews)</span>
                    </div>
                    <div className="DownloadDetails__content-meta">
                      <span className="file-type">{download.fileType}</span>
                      {download.sport && (
                        <span className="sport"> • {download.sport}</span>
                      )}
                      {/* {download.category && <span className="category"> • {download.category}</span>} */}
                      {download.difficulty && (
                        <span className="difficulty">
                          {" "}
                          • {download.difficulty}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                {/* Header with seller layout pattern */}
                <div className="bordrdiv">
                  <div className="DownloadDetails__header-container">
                    <div className="DownloadDetails__actions">
                      {!loadingReviews &&
                        !contentReviews.some(
                          (review) => review.user._id === user._id
                        ) && (
                          <button
                            className="DownloadDetails__review-btn"
                            onClick={() => setIsReviewModalOpen(true)}
                          >
                            Write a Review
                          </button>
                        )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="DownloadDetails__card mb-40 mt-30">
                {/* Order Information */}
                <div className="DownloadDetails__card-section DownloadDetails__card-section--order">
                  <h4 className="DownloadDetails__card-subtitle">
                    Order Information
                  </h4>
                  <div className="outerdivmain">
                    <div className="DownloadDetails__card-info-block">
                      <div className="DownloadDetails__card-detail">
                        <span className="DownloadDetails__card-detail-label">
                          Order Id:
                        </span>
                        <span className="DownloadDetails__card-detail-value">
                          #
                          {download.orderId?.slice(-8) ||
                            download._id?.slice(-8)}
                        </span>
                      </div>
                      <div className="DownloadDetails__card-detail">
                        <span className="DownloadDetails__card-detail-label">
                          Purchase Date:
                        </span>
                        <span className="DownloadDetails__card-detail-value">
                          {purchaseDate}
                        </span>
                      </div>
                      <div className="DownloadDetails__card-detail">
                        <span className="DownloadDetails__card-detail-label">
                          Downloads:
                        </span>
                        <span className="DownloadDetails__card-detail-value">
                          {download.downloadCount || 0} times
                        </span>
                      </div>
                    </div>
                    <div className="vertical-line"></div>
                    <div className="DownloadDetails__card-info-block">
                      {/* Subtitle for this block is not needed as per Figma */}
                      <div className="DownloadDetails__card-detail">
                        <span className="DownloadDetails__card-detail-label">
                          Content Type:
                        </span>
                        <span className="DownloadDetails__card-detail-value">
                          {download.fileType}
                        </span>
                      </div>
                      <div className="DownloadDetails__card-detail">
                        <span className="DownloadDetails__card-detail-label">
                          Amount Paid:
                        </span>
                        <span className="DownloadDetails__card-detail-value">
                          ${download.amount?.toFixed(2) || "0.00"}
                        </span>
                      </div>
                      <div className="DownloadDetails__card-detail">
                        <span className="DownloadDetails__card-detail-label">
                          Status:
                        </span>
                        <span className="DownloadDetails__card-detail-value">
                          <span className="status-badge completed">
                            {download.status}
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="DownloadDetails__card-divider"></div>
                {/* Customer and Payment Details */}
                <div className="DownloadDetails__card-section DownloadDetails__card-section--details">
                  <div className="DownloadDetails__card-info-block">
                    <h4 className="DownloadDetails__card-subtitle">
                      Customer Details
                    </h4>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Name:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.firstName} {user?.lastName}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Email Address:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.email}
                      </span>
                    </div>
                    <div className="DownloadDetails__card-detail">
                      <span className="DownloadDetails__card-detail-label">
                        Phone Number:
                      </span>
                      <span className="DownloadDetails__card-detail-value">
                        {user?.mobile || "Not provided"}
                      </span>
                    </div>
                  </div>
                  <div className="vertical-line" />
                  <div className="DownloadDetails__card-info-block">
                    <h4 className="DownloadDetails__card-subtitle">
                      Payment Details
                    </h4>
                    <div className="DownloadDetails__card-payment">
                      <span className="DownloadDetails__card-payment-value">
                        Payment Status:{" "}
                        <span className="status-badge completed">
                          {download.paymentStatus}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Preview */}
              <div className="DownloadDetails__video-section">
                <div className="DownloadDetails__section-header">
                  <h3 className="DownloadDetails__section-title">
                    Content Preview
                  </h3>
                  {download.fileUrl && (
                    <button
                      className="btn-outline DownloadDetails__previewBtn"
                      onClick={() => setIsPreviewModalOpen(true)}
                      title="Preview Document/Video in Full Screen"
                    >
                      <MdVisibility />
                      Preview
                    </button>
                  )}
                </div>
                <div className="DownloadDetails__video-container">
                  {download.fileType?.toLowerCase() === "video" ||
                    download.content?.contentType?.toLowerCase() === "video" ||
                    (download.fileUrl &&
                      /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(
                        download.fileUrl
                      )) ? (
                    <div className="DownloadDetails__video-player">
                      <video
                        ref={videoRef}
                        className="DownloadDetails__video-element"
                        poster={thumbnailUrl}
                        preload="metadata"
                        controls
                        controlsList="nodownload nofullscreen noremoteplayback"
                        disablePictureInPicture
                        src={download?.content?._id
                          ? getProxyUrlWithAuth(getProxyStreamUrl(download.content._id))
                          : signedFileUrl || getSmartFileUrl(download.fileUrl)}
                        onError={(e) => {
                          console.error("Video error:", e);
                          console.error("Video src:", e.target.src);
                          console.error(
                            "Video error code:",
                            e.target.error?.code
                          );
                          console.error(
                            "Video error message:",
                            e.target.error?.message
                          );
                          console.error(
                            "Expected signedFileUrl:",
                            signedFileUrl
                          );
                          console.error(
                            "Fallback URL:",
                            getSmartFileUrl(download.fileUrl)
                          );
                          console.error("Original fileUrl:", download.fileUrl);
                        }}
                        onLoadStart={() => {
                          //console.log("Video loading started");
                        }}
                        onCanPlay={() => console.log("Video can play")}
                        onLoadedMetadata={() =>
                          console.log("Video metadata loaded")
                        }
                      >
                        Your browser does not support the video tag.
                      </video>
                      {showDebugInfo && import.meta.env.DEV && (
                        <div
                          style={{
                            background: "#ffe6e6",
                            padding: "5px",
                            margin: "5px 0",
                            fontSize: "11px",
                          }}
                        >
                          <strong>Video Debug:</strong>
                          <br />
                          Video src:{" "}
                          {signedFileUrl || getSmartFileUrl(download.fileUrl)}
                          <br />
                          Signed URL: {signedFileUrl}
                          <br />
                          Poster: {thumbnailUrl}
                        </div>
                      )}
                    </div>
                  ) : download.fileType?.toLowerCase() === "pdf" ||
                    download.fileType?.toLowerCase() === "document" ? (
                    <div className="DownloadDetails__document-viewer">
                      {isLoadingSignedUrl ? (
                        <div className="DownloadDetails__loading">
                          <FaSync className="spinning" />
                          <p>Loading preview...</p>
                        </div>
                      ) : (
                        <DocumentViewer
                          fileUrl={download?.content?._id
                            ? getProxyUrlWithAuth(getProxyContentUrl(download.content._id))
                            : signedFileUrl || download.fileUrl}
                          fileName={
                            download.fileUrl?.split("/").pop() || "document"
                          }
                          title={download.title}
                          className="DownloadDetails__document-element"
                          height="950px"
                          showDownload={false}
                        />
                      )}

                    </div>
                  ) : download.fileType?.toLowerCase() === "audio" ? (
                    <div className="DownloadDetails__audio-player">
                      <audio
                        controls
                        className="DownloadDetails__audio-element"
                        preload="metadata"
                      >
                        <source
                          src={download?.content?._id
                            ? getProxyStreamUrl(download.content._id)
                            : signedFileUrl || getSmartFileUrl(download.fileUrl)}
                          type="audio/mpeg"
                        />
                        Your browser does not support the audio tag.
                      </audio>
                      <div className="DownloadDetails__audio-info">
                        <h4 className="DownloadDetails__audio-title">
                          {download.title}
                        </h4>
                        <p className="DownloadDetails__audio-coach">
                          By {download.coach}
                        </p>
                      </div>
                    </div>
                  ) : download.fileType?.toLowerCase() === "image" ? (
                    <div className="DownloadDetails__image-viewer">
                      <img
                        src={download?.content?._id
                          ? getProxyContentUrl(download.content._id)
                          : signedFileUrl || getSmartFileUrl(download.fileUrl)}
                        alt={download.title}
                        className="DownloadDetails__image-element"
                      />
                      <div className="DownloadDetails__image-title-overlay">
                        <h4 className="DownloadDetails__image-title">
                          {download.title}
                        </h4>
                      </div>
                    </div>
                  ) : (
                    <div className="DownloadDetails__file-preview">
                      <div className="DownloadDetails__file-icon-large">
                        {getFileIcon(download.fileType)}
                      </div>
                      <h4 className="DownloadDetails__file-title">
                        {download.title}
                      </h4>
                      <p className="DownloadDetails__file-type">
                        {download.fileType} File
                      </p>
                      <button
                        className="DownloadDetails__preview-download-btn"
                        onClick={handleDownload}
                        disabled={isDownloading}
                      >
                        {isDownloading ? (
                          <FaSync className="spinning" />
                        ) : (
                          <FaDownload />
                        )}
                        {isDownloading ? "Downloading..." : "Download to View"}
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Description */}
              <div className="DownloadDetails__description-section">
                <h3 className="DownloadDetails__section-title">Description</h3>
                <div
                  className="DownloadDetails__description-text"
                  dangerouslySetInnerHTML={{
                    __html:
                      download.content?.description ||
                      "<p>No description available.</p>",
                  }}
                />
                {download.content?.strategicContent && (
                  <div className="DownloadDetails__strategic-content">
                    <h4 className="DownloadDetails__strategic-title">
                      Strategic Content
                    </h4>
                    <div
                      className="DownloadDetails__strategic-text"
                      dangerouslySetInnerHTML={{
                        __html: download.content.strategicContent,
                      }}
                    />
                  </div>
                )}
              </div>

              {/* Reviews Section */}
              <div className="DownloadDetails__reviews-section">
                <div className="reviews-header">
                  <h3>Reviews</h3>
                </div>
                {loadingReviews ? (
                  <p>Loading reviews...</p>
                ) : contentReviews.length > 0 ? (
                  <div className="DownloadDetails__reviews-list">
                    {contentReviews.map((review) => (
                      <div
                        key={review._id}
                        className="DownloadDetails__review-item"
                      >
                        <div className="DownloadDetails__review-header">
                          <div className="DownloadDetails__review-user-info">
                            <h4>
                              {review.user?.firstName && review.user?.lastName
                                ? `${review.user.firstName} ${review.user.lastName}`
                                : review.user?.name ||
                                review.user?.username ||
                                "Anonymous"}
                            </h4>
                            <RatingStars rating={review.rating} size={20} />
                          </div>
                          {review.user?._id === user._id && (
                            <div className="DownloadDetails__review-actions">
                              <button onClick={() => handleEditReview(review)}>
                                Edit
                              </button>
                              <button
                                onClick={() => handleDeleteReview(review._id)}
                              >
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                        <h5>{review.title}</h5>
                        <p>{review.text}</p>
                        <span className="DownloadDetails__review-date">
                          {formatStandardDate(review.createdAt)}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p>No reviews yet</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <ReviewModal
        isOpen={isReviewModalOpen}
        onClose={handleModalClose}
        contentId={download.content._id}
        onReviewSubmitted={handleReviewSubmitted}
        existingReview={userReview}
      />

      {/* Preview Modal */}
      <PreviewModal
        isOpen={isPreviewModalOpen}
        onClose={() => setIsPreviewModalOpen(false)}
        fileUrl={download?.content?._id
          ? getProxyUrlWithAuth(getProxyContentUrl(download.content._id))
          : signedFileUrl || getSmartFileUrl(download.fileUrl)}
        fileName={download.fileUrl?.split("/").pop() || download.title}
        title={download.title}
        contentType={download.fileType || download.content?.contentType}
      />
    </div>
  );
};

export default DownloadDetails;
