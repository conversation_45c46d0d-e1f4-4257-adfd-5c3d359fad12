const mongoose = require('mongoose');
require('dotenv').config();

const debugDownloadsDetailed = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const Order = require('../models/Order');
    const CustomRequest = require('../models/CustomRequest');
    const User = require('../models/User');
    const Content = require('../models/Content');

    // Find the buyer
    const buyer = await User.findOne({ email: '<EMAIL>' });
    
    if (!buyer) {
      console.log('❌ Buyer not found.');
      return;
    }

    console.log(`🔍 Debugging downloads for buyer: ${buyer.firstName} ${buyer.lastName}`);
    console.log(`👤 Buyer ID: ${buyer._id}`);

    // Simulate the exact query from getBuyerDownloads
    const baseQuery = {
      buyer: buyer._id,
      paymentStatus: "Completed",
      $or: [
        // Regular content orders (not custom requests)
        { customRequestId: { $exists: false } },
        { customRequestId: null },
        // Custom request orders that are fully completed
        {
          customRequestId: { $exists: true, $ne: null },
        }
      ]
    };

    console.log('\n🔍 Running the exact query from getBuyerDownloads...');
    
    const allOrders = await Order.find(baseQuery)
      .populate({
        path: 'seller',
        select: 'firstName lastName email mobile'
      })
      .populate("content")
      .populate("customRequestId")
      .sort("-createdAt");

    console.log(`\n📦 Query found ${allOrders.length} orders:`);

    allOrders.forEach((order, index) => {
      console.log(`\n${index + 1}. Order ${order._id}:`);
      console.log(`   - Type: ${order.orderType}`);
      console.log(`   - Status: ${order.status}`);
      console.log(`   - Payment Status: ${order.paymentStatus}`);
      console.log(`   - Amount: $${order.amount}`);
      console.log(`   - Content: ${order.content ? order.content.title : 'None'}`);
      console.log(`   - Content ID: ${order.content ? order.content._id : 'None'}`);
      console.log(`   - Custom Request: ${order.customRequestId ? order.customRequestId.title : 'None'}`);
      console.log(`   - Custom Request ID: ${order.customRequestId ? order.customRequestId._id : 'None'}`);
      console.log(`   - Created: ${order.createdAt}`);
      
      // Check if this would be included in downloads
      if (order.customRequestId) {
        const customRequest = order.customRequestId;
        const isContentSubmitted = customRequest.contentSubmission?.isSubmitted;
        const isFullyPaid = customRequest.sellerResponse?.paymentType === 'full'
          ? customRequest.paymentDetails?.initialPaymentCompleted
          : (customRequest.paymentDetails?.initialPaymentCompleted && customRequest.paymentDetails?.finalPaymentCompleted);
        
        const shouldInclude = isContentSubmitted && isFullyPaid && customRequest.status === 'Completed';
        console.log(`   - Would be included in downloads: ${shouldInclude}`);
        
        if (shouldInclude) {
          console.log(`   - Download title would be: "${customRequest.title}"`);
          console.log(`   - Download type would be: "${order.content?.contentType || 'Unknown'}"`);
        }
      } else {
        console.log(`   - Would be included in downloads: true (regular content)`);
        if (order.content) {
          console.log(`   - Download title would be: "${order.content.title}"`);
          console.log(`   - Download type would be: "${order.content.contentType || 'Unknown'}"`);
        }
      }
    });

    // Check if there are any content records that might be causing issues
    console.log('\n🔍 Checking for any content with title "Custom download"...');
    const suspiciousContent = await Content.find({ 
      title: { $regex: /custom.*download/i } 
    });

    if (suspiciousContent.length > 0) {
      console.log(`\n⚠️  Found ${suspiciousContent.length} content records with "custom download" in title:`);
      suspiciousContent.forEach((content, index) => {
        console.log(`\n${index + 1}. Content ${content._id}:`);
        console.log(`   - Title: ${content.title}`);
        console.log(`   - Type: ${content.contentType}`);
        console.log(`   - Seller: ${content.seller}`);
        console.log(`   - Is Custom Content: ${content.isCustomContent}`);
        console.log(`   - Custom Request ID: ${content.customRequestId || 'None'}`);
        console.log(`   - Created: ${content.createdAt}`);
      });
    }

  } catch (error) {
    console.error('💥 Error debugging downloads:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n📡 MongoDB connection closed');
  }
};

// Run the debug
debugDownloadsDetailed();
