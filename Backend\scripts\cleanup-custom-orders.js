const mongoose = require('mongoose');
const { cleanupIncorrectCustomOrders } = require('../utils/accessControl');
require('dotenv').config();

const runCleanup = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    console.log('🧹 Starting cleanup of incorrect custom request orders...');

    const result = await cleanupIncorrectCustomOrders();

    if (result.success) {
      console.log('✅ Cleanup completed successfully!');
      console.log(`📊 Total orders checked: ${result.totalChecked}`);
      console.log(`🔧 Orders cleaned: ${result.cleanedCount}`);

      if (result.results.length > 0) {
        console.log('\n📋 Cleaned orders:');
        result.results.forEach((item, index) => {
          console.log(`  ${index + 1}. Order ${item.orderId} - ${item.action}`);
        });
      } else {
        console.log('✨ No incorrect orders found - database is clean!');
      }
    } else {
      console.error('❌ Cleanup failed:', result.error);
    }

  } catch (error) {
    console.error('💥 Error running cleanup:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('📡 MongoDB connection closed');
  }
};

// Run the cleanup
runCleanup();
