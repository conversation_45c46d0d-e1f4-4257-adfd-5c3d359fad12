const mongoose = require('mongoose');
require('dotenv').config();

const debugContentId = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const User = require('../models/User');
    const Content = require('../models/Content');

    // Import the controller function directly
    const { getBuyerCustomContent } = require('../controllers/requests');

    // Find the buyer
    const buyer = await User.findOne({ email: '<EMAIL>' });
    const targetContentId = '6895d0eff0f81ceb62dfb0e2'; // From the URL

    console.log(`🔍 Debugging content ID: ${targetContentId}`);
    console.log(`👤 Buyer ID: ${buyer._id}`);

    // Check if the content exists in the database
    const contentInDb = await Content.findById(targetContentId);

    if (contentInDb) {
      console.log(`\n✅ Content found in database:`);
      console.log(`   - ID: ${contentInDb._id}`);
      console.log(`   - Title: ${contentInDb.title}`);
      console.log(`   - Is Custom Content: ${contentInDb.isCustomContent}`);
      console.log(`   - Custom Request ID: ${contentInDb.customRequestId}`);
      console.log(`   - Seller: ${contentInDb.seller}`);
    } else {
      console.log(`\n❌ Content NOT found in database with ID: ${targetContentId}`);
    }

    // Test the custom content API
    const req = {
      user: { id: buyer._id.toString() },
      query: {}
    };

    let responseData = null;
    const res = {
      status: (code) => ({
        json: (data) => {
          responseData = data;
          return res;
        }
      })
    };

    const next = (error) => {
      console.error('❌ API Error:', error);
    };

    console.log('\n🔍 Testing custom content API...');
    await getBuyerCustomContent(req, res, next);

    if (responseData?.success && responseData?.data) {
      console.log(`\n📋 Custom content API returned ${responseData.data.length} items:`);

      responseData.data.forEach((content, index) => {
        console.log(`\n${index + 1}. Custom Content:`);
        console.log(`   - ID: ${content._id}`);
        console.log(`   - Title: ${content.title}`);
        console.log(`   - Matches target ID: ${content._id === targetContentId ? 'YES' : 'NO'}`);
        console.log(`   - ID type: ${typeof content._id}, Target type: ${typeof targetContentId}`);
        console.log(`   - ID toString: "${content._id.toString()}", Target: "${targetContentId}"`);
        console.log(`   - String match: ${content._id.toString() === targetContentId ? 'YES' : 'NO'}`);

        if (content._id.toString() === targetContentId) {
          console.log(`   🎯 FOUND MATCHING CONTENT!`);
          console.log(`   - Request Title: ${content.requestTitle}`);
          console.log(`   - Content Type: ${content.contentType}`);
          console.log(`   - File URL: ${content.fileUrl ? 'Present' : 'Missing'}`);
          console.log(`   - Completed At: ${content.completedAt}`);
        }
      });

      const foundContent = responseData.data.find(c => c._id.toString() === targetContentId);
      if (!foundContent) {
        console.log(`\n❌ Target content ID ${targetContentId} NOT found in API response`);
        console.log(`\n🔍 Available IDs in response:`);
        responseData.data.forEach((content, index) => {
          console.log(`   ${index + 1}. ${content._id}`);
        });
      }
    } else {
      console.log(`\n❌ Custom content API failed or returned no data`);
    }

  } catch (error) {
    console.error('💥 Error debugging content ID:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n📡 MongoDB connection closed');
  }
};

// Run the debug
debugContentId();
