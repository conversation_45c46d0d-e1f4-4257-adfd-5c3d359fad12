const mongoose = require('mongoose');
require('dotenv').config();

const debugDownloads = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const Order = require('../models/Order');
    const CustomRequest = require('../models/CustomRequest');
    const User = require('../models/User');
    const Content = require('../models/Content');

    // Find buyers with recent orders
    const recentOrders = await Order.find({})
      .populate('buyer')
      .sort('-createdAt')
      .limit(10);

    console.log('🔍 Recent buyers with orders:');
    const uniqueBuyers = [];
    recentOrders.forEach((order, index) => {
      if (order.buyer && !uniqueBuyers.find(b => b._id.toString() === order.buyer._id.toString())) {
        uniqueBuyers.push(order.buyer);
        console.log(`${uniqueBuyers.length}. ${order.buyer.firstName} ${order.buyer.lastName} (${order.buyer.email})`);
      }
    });

    // Use the first buyer or find by partial email
    let buyer = uniqueBuyers.find(b => b.email.toLowerCase().includes('patel')) || uniqueBuyers[0];

    if (!buyer) {
      console.log('❌ No buyers found with recent orders.');
      return;
    }

    console.log(`🔍 Debugging downloads for buyer: ${buyer.firstName} ${buyer.lastName} (${buyer.email})`);
    console.log(`👤 Buyer ID: ${buyer._id}`);

    // Get all orders for this buyer
    const allOrders = await Order.find({ buyer: buyer._id })
      .populate('content')
      .populate('customRequestId')
      .sort('-createdAt');

    console.log(`\n📦 Found ${allOrders.length} total orders for this buyer:`);

    allOrders.forEach((order, index) => {
      console.log(`\n${index + 1}. Order ${order._id}:`);
      console.log(`   - Type: ${order.orderType}`);
      console.log(`   - Status: ${order.status}`);
      console.log(`   - Payment Status: ${order.paymentStatus}`);
      console.log(`   - Amount: $${order.amount}`);
      console.log(`   - Content: ${order.content ? order.content.title : 'None'}`);
      console.log(`   - Custom Request: ${order.customRequestId ? order.customRequestId.title : 'None'}`);
      console.log(`   - Created: ${order.createdAt}`);
    });

    // Check specifically for completed orders
    const completedOrders = allOrders.filter(order =>
      order.paymentStatus === 'Completed' &&
      (order.status === 'Completed' || order.status === 'Processing')
    );

    console.log(`\n✅ Completed orders that would show in downloads: ${completedOrders.length}`);

    completedOrders.forEach((order, index) => {
      console.log(`\n${index + 1}. DOWNLOAD ORDER ${order._id}:`);
      console.log(`   - Type: ${order.orderType}`);
      console.log(`   - Content: ${order.content ? order.content.title : 'None'}`);
      console.log(`   - Content ID: ${order.content ? order.content._id : 'None'}`);
      console.log(`   - Custom Request: ${order.customRequestId ? order.customRequestId.title : 'None'}`);
      console.log(`   - Custom Request ID: ${order.customRequestId ? order.customRequestId._id : 'None'}`);

      if (order.customRequestId && order.content) {
        console.log(`   ⚠️  POTENTIAL ISSUE: Custom order has content reference!`);
        console.log(`   - Related Content: ${order.customRequestId.relatedContent || 'None'}`);
        if (order.customRequestId.relatedContent &&
          order.content._id.toString() === order.customRequestId.relatedContent.toString()) {
          console.log(`   🚨 CONFIRMED ISSUE: Order content matches custom request's related content!`);
        }
      }
    });

    // Get custom requests for this buyer
    const customRequests = await CustomRequest.find({ buyer: buyer._id })
      .populate('relatedContent')
      .sort('-createdAt');

    console.log(`\n📋 Found ${customRequests.length} custom requests for this buyer:`);

    customRequests.forEach((request, index) => {
      console.log(`\n${index + 1}. Custom Request ${request._id}:`);
      console.log(`   - Title: ${request.title}`);
      console.log(`   - Status: ${request.status}`);
      console.log(`   - Related Content: ${request.relatedContent ? request.relatedContent.title : 'None'}`);
      console.log(`   - Payment Type: ${request.sellerResponse?.paymentType || 'None'}`);
      console.log(`   - Initial Payment: ${request.paymentDetails?.initialPaymentCompleted || false}`);
      console.log(`   - Final Payment: ${request.paymentDetails?.finalPaymentCompleted || false}`);
      console.log(`   - Content Submitted: ${request.contentSubmission?.isSubmitted || false}`);
    });

  } catch (error) {
    console.error('💥 Error debugging downloads:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n📡 MongoDB connection closed');
  }
};

// Run the debug
debugDownloads();
