const Order = require('../models/Order');
const Content = require('../models/Content');

/**
 * Check if a user has purchased specific content
 * @param {string} userId - User ID
 * @param {string} contentId - Content ID
 * @returns {Promise<boolean>} - Whether user has purchased the content
 */
const hasUserPurchasedContent = async (userId, contentId) => {
  try {
    if (!userId || !contentId) {
      return false;
    }

    const order = await Order.findOne({
      buyer: userId,
      content: contentId,
      paymentStatus: 'Completed',
      status: { $in: ['Completed', 'Processing'] }
    });

    return !!order;
  } catch (error) {
    console.error('Error checking user purchase status:', error);
    return false;
  }
};

/**
 * Check if user has access to custom content
 * @param {string} userId - User ID
 * @param {string} contentId - Content ID
 * @returns {Promise<boolean>} - True if user has access to custom content
 */
const hasUserCustomContentAccess = async (userId, contentId) => {
  try {
    if (!userId || !contentId) {
      return false;
    }

    const CustomRequest = require('../models/CustomRequest');

    // Check if this is custom content
    const content = await Content.findById(contentId);
    if (!content || !content.isCustomContent || !content.customRequestId) {
      return false;
    }

    // Check if user is the buyer of the custom request and payment is completed
    const customRequest = await CustomRequest.findById(content.customRequestId);
    if (!customRequest) {
      return false;
    }

    // User must be the buyer and have completed payment
    const isBuyer = customRequest.buyer.toString() === userId;
    const hasCompletedPayment = customRequest.sellerResponse.paymentType === 'full'
      ? customRequest.paymentDetails.initialPaymentCompleted
      : (customRequest.paymentDetails.initialPaymentCompleted && customRequest.paymentDetails.finalPaymentCompleted);

    return isBuyer && hasCompletedPayment;
  } catch (error) {
    console.error('Error checking custom content access:', error);
    return false;
  }
};

/**
 * Check if user can access full content or only preview
 * @param {string} userId - User ID (can be null for unauthenticated users)
 * @param {string} contentId - Content ID
 * @param {string} sellerId - Content seller ID
 * @param {string} userRole - User role (admin, seller, buyer, etc.)
 * @param {boolean} isCustomContent - Whether this is custom content
 * @returns {Promise<Object>} - Access information
 */
const getContentAccessLevel = async (userId, contentId, sellerId, userRole = null, isCustomContent = false) => {
  try {
    // Admin users have full access
    if (userRole === 'admin') {
      return {
        canAccessFull: true,
        canAccessPreview: true,
        reason: 'admin_access'
      };
    }

    // Content seller has full access to their own content
    if (userId && userId === sellerId) {
      return {
        canAccessFull: true,
        canAccessPreview: true,
        reason: 'owner_access'
      };
    }

    // For custom content, check if user is the intended buyer first
    if (userId && isCustomContent) {
      const hasCustomAccess = await hasUserCustomContentAccess(userId, contentId);
      if (hasCustomAccess) {
        return {
          canAccessFull: true,
          canAccessPreview: true,
          reason: 'custom_content_access'
        };
      }
    }

    // Check if user has purchased the content (for regular content)
    if (userId && !isCustomContent) {
      const hasPurchased = await hasUserPurchasedContent(userId, contentId);
      if (hasPurchased) {
        return {
          canAccessFull: true,
          canAccessPreview: true,
          reason: 'purchased_access'
        };
      }
    }

    // For custom content, if user is not the intended buyer, deny all access
    if (isCustomContent) {
      return {
        canAccessFull: false,
        canAccessPreview: false,
        reason: 'custom_content_restricted'
      };
    }

    // Default for regular content: only preview access
    return {
      canAccessFull: false,
      canAccessPreview: true,
      reason: 'preview_only'
    };
  } catch (error) {
    console.error('Error determining content access level:', error);
    return {
      canAccessFull: false,
      canAccessPreview: isCustomContent ? false : true,
      reason: 'error_default_preview'
    };
  }
};

/**
 * Get appropriate file URL based on user's access level
 * @param {Object} content - Content document
 * @param {string} userId - User ID
 * @param {string} userRole - User role
 * @returns {Promise<Object>} - File access information
 */
const getContentFileAccess = async (content, userId = null, userRole = null) => {
  try {
    // Handle both populated and non-populated seller field
    const sellerId = content.seller._id ? content.seller._id.toString() : content.seller.toString();

    const accessLevel = await getContentAccessLevel(
      userId,
      content._id.toString(),
      sellerId,
      userRole,
      content.isCustomContent || false
    );

    let fileUrl = null;
    let accessType = 'none';

    if (accessLevel.canAccessFull) {
      fileUrl = content.fileUrl;
      accessType = 'full';
    } else if (accessLevel.canAccessPreview && content.previewUrl) {
      fileUrl = content.previewUrl;
      accessType = 'preview';
    }

    return {
      fileUrl,
      accessType,
      reason: accessLevel.reason,
      canAccessFull: accessLevel.canAccessFull,
      canAccessPreview: accessLevel.canAccessPreview,
      hasPreview: !!content.previewUrl
    };
  } catch (error) {
    console.error('Error getting content file access:', error);
    return {
      fileUrl: null,
      accessType: 'none',
      reason: 'error',
      canAccessFull: false,
      canAccessPreview: false,
      hasPreview: false
    };
  }
};

/**
 * Middleware to check content access permissions
 * @param {string} requiredAccess - 'full' or 'preview'
 * @returns {Function} - Express middleware function
 */
const checkContentAccess = (requiredAccess = 'preview') => {
  return async (req, res, next) => {
    try {
      const contentId = req.params.id || req.params.contentId;
      const userId = req.user ? req.user.id : null;
      const userRole = req.user ? req.user.role : null;

      // Get content
      const content = await Content.findById(contentId);
      if (!content) {
        return res.status(404).json({
          success: false,
          message: 'Content not found'
        });
      }

      // Check access level
      const accessLevel = await getContentAccessLevel(
        userId,
        contentId,
        content.seller.toString(),
        userRole
      );

      // Determine if user has required access
      let hasRequiredAccess = false;
      if (requiredAccess === 'full') {
        hasRequiredAccess = accessLevel.canAccessFull;
      } else if (requiredAccess === 'preview') {
        hasRequiredAccess = accessLevel.canAccessPreview;
      }

      if (!hasRequiredAccess) {
        return res.status(403).json({
          success: false,
          message: `Access denied. Required access level: ${requiredAccess}`,
          accessLevel: accessLevel
        });
      }

      // Add access information to request
      req.contentAccess = accessLevel;
      req.content = content;

      next();
    } catch (error) {
      console.error('Error in checkContentAccess middleware:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking content access'
      });
    }
  };
};

/**
 * Get user's purchase history for content access validation
 * @param {string} userId - User ID
 * @returns {Promise<Array>} - Array of purchased content IDs
 */
const getUserPurchasedContentIds = async (userId) => {
  try {
    if (!userId) {
      return [];
    }

    const orders = await Order.find({
      buyer: userId,
      paymentStatus: 'Completed',
      status: { $in: ['Completed', 'Processing'] }
    }).select('content');

    return orders.map(order => order.content.toString());
  } catch (error) {
    console.error('Error getting user purchased content IDs:', error);
    return [];
  }
};

module.exports = {
  hasUserPurchasedContent,
  getContentAccessLevel,
  getContentFileAccess,
  checkContentAccess,
  getUserPurchasedContentIds
};
