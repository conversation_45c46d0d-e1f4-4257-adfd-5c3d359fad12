/* Buyer Request Detail Styles - Following consistent dashboard pattern */
.buyer-request-detail {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
}

/* Back Button - Following consistent button pattern */
.buyer-request-detail .back-btn {
  display: flex;
  align-items: center;
  gap: var(--border-radius-medium);
  padding: 10px var(--basefont);
  background: var(--bg-gray);
  border: none;
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: var(--heading5);
}

.buyer-request-detail .back-btn:hover {
  background: var(--primary-light-color);
  color: var(--primary-color);
  transform: scale(1.02);
}

/* Request Overview */
.buyer-request-detail .request-overview {
  margin-bottom: var(--heading4);
}

.buyer-request-detail .overview-header {
  margin-bottom: var(--heading5);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.buyer-request-detail .request-title-section {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--smallfont);
}

.buyer-request-detail .request-title {
  margin: 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 700;
}

.buyer-request-detail .request-meta {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.buyer-request-detail .request-id {
  font-family: monospace;
  background: var(--bg-gray);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  font-weight: 500;
}

.buyer-request-detail .request-date {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

/* Action Section - Following consistent alert pattern */
.buyer-request-detail .action-section {
  margin: var(--heading5) 0;
  padding: var(--basefont);
  background: var(--primary-light-color);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
  box-shadow: var(--box-shadow-light);
}

.buyer-request-detail .payment-btn {
  display: flex;
  align-items: center;
  gap: var(--border-radius-medium);
  padding: 10px var(--heading5);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.buyer-request-detail .payment-btn:hover {
  background: var(--secondary-color);
  transform: scale(1.02);
}

/* Overview Grid - Following consistent table pattern */
.buyer-request-detail .overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--heading5);
}

.buyer-request-detail .overview-card {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
}

.buyer-request-detail .card-header {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 12px 10px;
  background: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
}

.buyer-request-detail .card-icon {
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.buyer-request-detail .card-header h3 {
  margin: 0;
  font-size: var(--extrasmallfont);
  color: var(--secondary-color);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.buyer-request-detail .card-content {
  padding: 12px 10px;
}

/* Seller Details - Following consistent info display pattern */
.buyer-request-detail .seller-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.buyer-request-detail .seller-name {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
}

.buyer-request-detail .seller-email {
  font-size: var(--basefont);
  color: var(--dark-gray);
}

/* Budget Details - Following consistent table data display pattern */
.buyer-request-detail .budget-details,
.buyer-request-detail .request-details {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.buyer-request-detail .budget-item,
.buyer-request-detail .detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.buyer-request-detail .budget-item:last-child,
.buyer-request-detail .detail-item:last-child {
  border-bottom: none;
}

.buyer-request-detail .budget-item .label,
.buyer-request-detail .detail-item .label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.buyer-request-detail .budget-item .value,
.buyer-request-detail .detail-item .value {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.buyer-request-detail .budget-item .value.completed {
  color: #2ecc71;
  font-weight: 700;
}

.buyer-request-detail .budget-item .value.pending {
  color: #f39c12;
  font-weight: 700;
}

/* Status Badge - Following consistent badge pattern */
.buyer-request-detail .status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: capitalize;
}

.buyer-request-detail .status-orange {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.buyer-request-detail .status-green {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.buyer-request-detail .status-red {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.buyer-request-detail .status-blue {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.buyer-request-detail .status-purple {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.buyer-request-detail .status-gray {
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Request Sections - Following consistent table section pattern */
.buyer-request-detail .request-section {
  margin-bottom: var(--heading4);
  padding: var(--heading5);
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
}

.buyer-request-detail .section-title {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 600;
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--extrasmallfont);
}

.buyer-request-detail .description-content {
  background: var(--bg-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--dark-gray);
  margin-top: var(--basefont);
}

.buyer-request-detail .description-content p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--secondary-color);
  line-height: 1.6;
}

/* Response Content - Following consistent content pattern */
.buyer-request-detail .response-content,
.buyer-request-detail .delivery-content {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.buyer-request-detail .response-status,
.buyer-request-detail .delivery-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.buyer-request-detail .response-badge,
.buyer-request-detail .delivery-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
}

.buyer-request-detail .response-badge.accepted,
.buyer-request-detail .delivery-badge {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.buyer-request-detail .response-badge.rejected {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.buyer-request-detail .response-date,
.buyer-request-detail .delivery-date {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.buyer-request-detail .response-message,
.buyer-request-detail .delivery-message {
  background: var(--bg-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-color);
}

.buyer-request-detail .response-message h4,
.buyer-request-detail .delivery-message h4 {
  margin: 0 0 var(--extrasmallfont) 0;
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

.buyer-request-detail .response-message p,
.buyer-request-detail .delivery-message p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--text-color);
  line-height: 1.5;
}

.buyer-request-detail .delivered-content {
  display: flex;
  justify-content: flex-start;
  margin-top: var(--basefont);
}

/* Empty State - Following consistent empty state pattern */
.buyer-request-detail .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
  background: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.buyer-request-detail .empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

.buyer-request-detail .empty-state h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
  font-weight: 600;
}

.buyer-request-detail .empty-state p {
  margin: 0 0 var(--heading5) 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
  max-width: 400px;
  line-height: 1.5;
}

/* Responsive Design - Following consistent responsive pattern */
@media (max-width: 1024px) {
  .buyer-request-detail .overview-grid {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }
}

@media (max-width: 768px) {
  .buyer-request-detail .request-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .buyer-request-detail .request-title {
    font-size: var(--heading5);
  }

  .buyer-request-detail .request-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .buyer-request-detail .budget-item,
  .buyer-request-detail .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
    padding: 12px 0;
  }

  .buyer-request-detail .response-status,
  .buyer-request-detail .delivery-status {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .buyer-request-detail .request-section {
    padding: var(--basefont);
  }

  .buyer-request-detail .card-content {
    padding: 12px 10px;
  }
}

@media (max-width: 480px) {
  .buyer-request-detail .back-btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--smallfont);
  }

  .buyer-request-detail .request-title {
    font-size: var(--heading5);
  }

  .buyer-request-detail .overview-grid {
    gap: var(--smallfont);
  }
}
