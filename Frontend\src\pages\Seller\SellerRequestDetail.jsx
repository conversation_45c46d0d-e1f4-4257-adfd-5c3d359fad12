import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";

import SellerLayout from "../../components/seller/SellerLayout";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import RequestResponseModal from "../../components/seller/RequestResponseModal";
import CustomContentUploadModal from "../../components/seller/CustomContentUploadModal";
import "../../styles/SellerRequestDetail.css";
import { 
  FiArrowLeft, 
  FiUser, 
  FiCalendar, 
  FiDollarSign, 
  FiFileText, 
  FiCheck, 
  FiX, 
  FiClock,
  FiUpload,
  FiEye
} from "react-icons/fi";
import { MdRequestPage } from "react-icons/md";
import { toast } from "react-toastify";
import api from "../../services/api";
import { formatStandardDate } from "../../utils/dateValidation";

const SellerRequestDetail = () => {
  const { id: requestId } = useParams();
  const navigate = useNavigate();
  // Local state
  const [request, setRequest] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isResponseModalOpen, setIsResponseModalOpen] = useState(false);
  const [isContentUploadModalOpen, setIsContentUploadModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch request details
  useEffect(() => {
    const fetchRequestDetails = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/requests/${requestId}`);
        setRequest(response.data.data);
      } catch (error) {
        console.error('Error fetching request details:', error);
        setError(error.response?.data?.message || 'Failed to load request details');
      } finally {
        setLoading(false);
      }
    };

    if (requestId) {
      fetchRequestDetails();
    }
  }, [requestId]);

  const handleResponseSubmit = async (responseData) => {
    setIsSubmitting(true);
    try {
      await api.put(`/requests/${requestId}/respond`, responseData);
      toast.success(`Request ${responseData.accepted ? 'accepted' : 'rejected'} successfully!`);
      setIsResponseModalOpen(false);
      // Refresh request data
      const response = await api.get(`/requests/${requestId}`);
      setRequest(response.data.data);
    } catch (error) {
      console.error('Error responding to request:', error);
      toast.error(error.response?.data?.message || 'Failed to respond to request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRequestRemainingPayment = async () => {
    try {
      await api.put(`/requests/${requestId}/request-remaining-payment`);
      toast.success('Remaining payment requested successfully!');
      // Refresh request data
      const response = await api.get(`/requests/${requestId}`);
      setRequest(response.data.data);
    } catch (error) {
      console.error('Error requesting remaining payment:', error);
      toast.error(error.response?.data?.message || 'Failed to request remaining payment');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'Pending': { color: 'orange', icon: <FiClock /> },
      'Accepted': { color: 'green', icon: <FiCheck /> },
      'Rejected': { color: 'red', icon: <FiX /> },
      'In Progress': { color: 'blue', icon: <FiClock /> },
      'Content Submitted': { color: 'purple', icon: <FiCheck /> },
      'Completed': { color: 'green', icon: <FiCheck /> }
    };

    const config = statusConfig[status] || { color: 'gray', icon: <FiClock /> };
    
    return (
      <span className={`status-badge status-${config.color}`}>
        {config.icon}
        {status}
      </span>
    );
  };

  const canRespond = request?.status === 'Pending';
  const canRequestPayment = request?.status === 'In Progress' && 
                            request?.sellerResponse?.paymentType === 'half' && 
                            request?.paymentDetails?.initialPaymentCompleted && 
                            !request?.remainingPaymentRequested;
  const canSubmitContent = ['In Progress', 'Accepted'].includes(request?.status) &&
                          (request?.sellerResponse?.paymentType === 'full' ? 
                           request?.paymentDetails?.initialPaymentCompleted : 
                           request?.paymentDetails?.finalPaymentCompleted);

  if (loading) {
    return (
      <SellerLayout>
        <div className="request-detail-container">
          <LoadingSkeleton count={8} height="60px" />
        </div>
      </SellerLayout>
    );
  }

  if (error) {
    return (
      <SellerLayout>
        <div className="request-detail-container">
          <ErrorDisplay
            error={error}
            onRetry={() => window.location.reload()}
            title="Failed to load request details"
          />
        </div>
      </SellerLayout>
    );
  }

  if (!request) {
    return (
      <SellerLayout>
        <div className="request-detail-container">
          <div className="empty-state">
            <MdRequestPage className="empty-icon" />
            <h3>Request Not Found</h3>
            <p>The requested custom request could not be found.</p>
            <button 
              className="btn-primary"
              onClick={() => navigate('/seller/requests')}
            >
              Back to Requests
            </button>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="request-detail-container">
     

        {/* Request Overview */}
        <div className="request-overview">
          <div className="overview-header request-section">
            <div className="request-title-section">
              <h1 className="request-title">{request.title}</h1>
              {getStatusBadge(request.status)}
            </div>
            <div className="request-meta">
              <span className="request-id">Request #{request._id.slice(-8)}</span>
              <span className="request-date">
                <FiCalendar />
                {formatStandardDate(request.createdAt)}
              </span>
            </div>
          </div>

          <div className="overview-grid">
            <div className="overview-card">
              <div className="card-header">
                <FiUser className="card-icon" />
                <h3>Buyer Information</h3>
              </div>
              <div className="card-content">
                <div className="buyer-details">
                  <div className="buyer-name">
                    {request.buyer?.firstName} {request.buyer?.lastName}
                  </div>
                  <div className="buyer-email">{request.buyer?.email}</div>
                </div>
              </div>
            </div>

            <div className="overview-card">
              <div className="card-header">
                <FiDollarSign className="card-icon" />
                <h3>Budget & Payment</h3>
              </div>
              <div className="card-content">
                <div className="budget-details">
                  <div className="budget-item">
                    <span className="label">Requested Budget:</span>
                    <span className="value">${request.budget}</span>
                  </div>
                  {request.sellerResponse?.price && (
                    <div className="budget-item">
                      <span className="label">Your Price:</span>
                      <span className="value">${request.sellerResponse.price}</span>
                    </div>
                  )}
                  {request.sellerResponse?.paymentType && (
                    <div className="budget-item">
                      <span className="label">Payment Type:</span>
                      <span className="value">
                        {request.sellerResponse.paymentType === 'half' ? 'Half Payment' : 'Full Payment'}
                      </span>
                    </div>
                  )}
                  {request.paymentDetails && (
                    <>
                      <div className="budget-item">
                        <span className="label">Total Amount:</span>
                        <span className="value">${request.paymentDetails.totalAmount}</span>
                      </div>
                      <div className="budget-item">
                        <span className="label">Paid Amount:</span>
                        <span className="value">${request.paymentDetails.paidAmount}</span>
                      </div>
                      {request.paymentDetails.remainingAmount > 0 && (
                        <div className="budget-item">
                          <span className="label">Remaining Amount:</span>
                          <span className="value">${request.paymentDetails.remainingAmount}</span>
                        </div>
                      )}
                      <div className="budget-item">
                        <span className="label">Initial Payment:</span>
                        <span className={`value ${request.paymentDetails.initialPaymentCompleted ? 'completed' : 'pending'}`}>
                          {request.paymentDetails.initialPaymentCompleted ? 'Completed' : 'Pending'}
                        </span>
                      </div>
                      {request.sellerResponse?.paymentType === 'half' && (
                        <div className="budget-item">
                          <span className="label">Final Payment:</span>
                          <span className={`value ${request.paymentDetails.finalPaymentCompleted ? 'completed' : 'pending'}`}>
                            {request.paymentDetails.finalPaymentCompleted ? 'Completed' : 'Pending'}
                          </span>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="overview-card">
              <div className="card-header">
                <FiFileText className="card-icon" />
                <h3>Request Details</h3>
              </div>
              <div className="card-content">
                <div className="request-details">
                  <div className="detail-item">
                    <span className="label">Content Type:</span>
                    <span className="value">{request.contentType}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Sport:</span>
                    <span className="value">{request.sport}</span>
                  </div>
                  {request.requestedDeliveryDate && (
                    <div className="detail-item">
                      <span className="label">Requested Delivery:</span>
                      <span className="value">{formatStandardDate(request.requestedDeliveryDate)}</span>
                    </div>
                  )}
                  {request.sellerResponse?.estimatedDeliveryDate && (
                    <div className="detail-item">
                      <span className="label">Estimated Delivery:</span>
                      <span className="value">{formatStandardDate(request.sellerResponse.estimatedDeliveryDate)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Request Description */}
        <div className="request-section">
          <h2 className="section-title">Request Description</h2>
          <div className="description-content">
            <p>{request.description}</p>
          </div>
        </div>

        {/* Response Section */}
        {request.sellerResponse && (
          <div className="request-section">
            <h2 className="section-title">Your Response</h2>
            <div className="response-content">
              <div className="response-status">
                <span className={`response-badge ${request.sellerResponse.accepted ? 'accepted' : 'rejected'}`}>
                  {request.sellerResponse.accepted ? <FiCheck /> : <FiX />}
                  {request.sellerResponse.accepted ? 'Accepted' : 'Rejected'}
                </span>
                <span className="response-date">
                  {formatStandardDate(request.sellerResponse.responseDate)}
                </span>
              </div>
              {request.sellerResponse.message && (
                <div className="response-message">
                  <h4>Your Message:</h4>
                  <p>{request.sellerResponse.message}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Content Submission Section */}
        {request.contentSubmission?.isSubmitted && (
          <div className="request-section">
            <h2 className="section-title">Content Submission</h2>
            <div className="submission-content">
              <div className="submission-status">
                <span className="submission-badge">
                  <FiCheck />
                  Content Submitted
                </span>
                <span className="submission-date">
                  {formatStandardDate(request.contentSubmission.submittedAt)}
                </span>
              </div>
              {request.contentSubmission.submissionMessage && (
                <div className="submission-message">
                  <h4>Submission Message:</h4>
                  <p>{request.contentSubmission.submissionMessage}</p>
                </div>
              )}
              {request.contentSubmission.contentId && (
                <div className="submitted-content">
                  <button 
                    className="btn-outline"
                    onClick={() => navigate(`/seller/content/${request.contentSubmission.contentId}`)}
                  >
                    <FiEye />
                    View Submitted Content
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Modals */}
        {isResponseModalOpen && (
          <RequestResponseModal
            isOpen={isResponseModalOpen}
            onClose={() => setIsResponseModalOpen(false)}
            request={request}
            onSubmit={handleResponseSubmit}
            isSubmitting={isSubmitting}
          />
        )}

        {/* Custom Content Upload Modal */}
        {isContentUploadModalOpen && (
          <CustomContentUploadModal
            isOpen={isContentUploadModalOpen}
            onClose={() => setIsContentUploadModalOpen(false)}
            request={request}
            onSuccess={() => {
              // Refresh request data after successful submission
              fetchRequestDetails();
            }}
          />
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerRequestDetail;
