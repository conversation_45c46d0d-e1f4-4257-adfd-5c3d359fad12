const mongoose = require('mongoose');
require('dotenv').config();

const findProblematicOrder = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const Order = require('../models/Order');
    const User = require('../models/User');
    const Content = require('../models/Content');

    // Find the buyer
    const buyer = await User.findOne({ email: '<EMAIL>' });
    const problematicContentId = '6895910a04c0692741e6d319'; // "Custom download" content

    console.log(`🔍 Looking for orders for content ID: ${problematicContentId}`);
    console.log(`👤 Buyer ID: ${buyer._id}`);

    // Find ALL orders for this specific content
    const allOrdersForContent = await Order.find({ 
      content: problematicContentId 
    })
    .populate('buyer', 'firstName lastName email')
    .populate('seller', 'firstName lastName email')
    .populate('content')
    .populate('customRequestId')
    .sort('-createdAt');

    console.log(`\n📦 Found ${allOrdersForContent.length} orders for this content:`);

    allOrdersForContent.forEach((order, index) => {
      console.log(`\n${index + 1}. Order ${order._id}:`);
      console.log(`   - Buyer: ${order.buyer?.firstName} ${order.buyer?.lastName} (${order.buyer?.email})`);
      console.log(`   - Seller: ${order.seller?.firstName} ${order.seller?.lastName}`);
      console.log(`   - Type: ${order.orderType}`);
      console.log(`   - Status: ${order.status}`);
      console.log(`   - Payment Status: ${order.paymentStatus}`);
      console.log(`   - Amount: $${order.amount}`);
      console.log(`   - Content: ${order.content?.title}`);
      console.log(`   - Custom Request ID: ${order.customRequestId || 'None'}`);
      console.log(`   - Created: ${order.createdAt}`);
      
      // Check if this is the problematic order
      if (order.buyer?._id.toString() === buyer._id.toString() && 
          order.paymentStatus === 'Completed') {
        console.log(`   🚨 THIS IS THE PROBLEMATIC ORDER! Buyer has completed order for content they didn't purchase.`);
        
        if (order.customRequestId) {
          console.log(`   🔍 This order has a custom request ID: ${order.customRequestId}`);
          console.log(`   💡 This suggests the order was created incorrectly during custom request processing.`);
        }
      }
    });

    // Check specifically for the buyer's orders for this content
    const buyerOrdersForContent = await Order.find({ 
      buyer: buyer._id,
      content: problematicContentId,
      paymentStatus: 'Completed'
    });

    if (buyerOrdersForContent.length > 0) {
      console.log(`\n🚨 CONFIRMED: Buyer has ${buyerOrdersForContent.length} completed order(s) for this content!`);
      console.log(`\n🛠️  SOLUTION: Delete these incorrect orders to fix the downloads.`);
      
      buyerOrdersForContent.forEach((order, index) => {
        console.log(`\nOrder to delete ${index + 1}: ${order._id}`);
      });
    } else {
      console.log(`\n✅ No completed orders found for this buyer and content.`);
      console.log(`\n🤔 The issue might be elsewhere. Let's check the downloads API response.`);
    }

  } catch (error) {
    console.error('💥 Error finding problematic order:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n📡 MongoDB connection closed');
  }
};

// Run the debug
findProblematicOrder();
