const express = require('express');
const { check } = require('express-validator');
const {
  getRequests,
  getRequest,
  createRequest,
  respondToRequest,
  cancelRequest,
  getBuyerRequests,
  getSellerRequests,
  requestRemainingPayment,
  submitCustomContent,
  getBuyerCustomContent,
  debugBuyerCustomContent,
  createPaymentOrder,
  testSubmitContent
} = require('../controllers/requests');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protected routes
router.use(protect);

// Buyer routes
router.get('/buyer', authorize('buyer', 'admin'), getBuyerRequests);
router.get('/buyer/content', authorize('buyer', 'admin'), getBuyerCustomContent);
router.get('/buyer/content/debug', authorize('buyer', 'admin'), debugBuyerCustomContent);
router.post('/:id/create-payment-order', authorize('buyer', 'admin'), createPaymentOrder);
router.put('/:id/cancel', authorize('buyer', 'admin'), cancelRequest);

router.post(
  '/',
  authorize('buyer'),
  [
    check('sellerId', 'Seller ID is required').not().isEmpty(),
    check('relatedContentId', 'Related content ID is required').not().isEmpty(),
    check('title', 'Title is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('sport', 'Sport is required').not().isEmpty(),
    check('contentType', 'Content type is required').not().isEmpty(),
    check('budget', 'Budget is required and must be a positive number').isFloat({ min: 0.01 })
  ],
  createRequest
);

// Seller routes
router.get('/seller', authorize('seller', 'admin'), getSellerRequests);

router.put(
  '/:id/respond',
  authorize('seller'),
  [
    check('accepted', 'Accepted status is required').isBoolean(),
    check('paymentType', 'Payment type is required when accepting').optional().isIn(['full', 'half']),
    check('price', 'Price is required when accepting').optional().isFloat({ min: 0.01 }),
    check('estimatedDeliveryDate', 'Estimated delivery date must be a valid date').optional().isISO8601(),
    check('message', 'Message cannot be more than 1000 characters').optional().isLength({ max: 1000 })
  ],
  respondToRequest
);

router.put('/:id/request-remaining-payment', authorize('seller'), requestRemainingPayment);

router.put(
  '/:id/submit-content',
  authorize('seller'),
  [
    check('contentId', 'Content ID is required').not().isEmpty(),
    check('submissionMessage', 'Submission message cannot be more than 500 characters').optional().isLength({ max: 500 })
  ],
  submitCustomContent
);

// DEPRECATED: POST /:id/submit route removed
// Use PUT /:id/submit-content instead for proper payment validation

// Test route for multiple submissions
router.post('/:id/test-submit-content', authorize('admin'), [
  check('contentId', 'Content ID is required').not().isEmpty(),
  check('submissionMessage', 'Submission message cannot be more than 500 characters').optional().isLength({ max: 500 })
], testSubmitContent);

// Admin routes
router.get('/', authorize('admin'), getRequests);

// Common routes
router.get('/:id', getRequest);

module.exports = router;
