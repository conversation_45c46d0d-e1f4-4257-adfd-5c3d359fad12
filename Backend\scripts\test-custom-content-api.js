const mongoose = require('mongoose');
require('dotenv').config();

const testCustomContentAPI = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    const User = require('../models/User');
    
    // Import the controller function directly
    const { getBuyerCustomContent } = require('../controllers/requests');

    // Find the buyer
    const buyer = await User.findOne({ email: '<EMAIL>' });
    
    if (!buyer) {
      console.log('❌ Buyer not found.');
      return;
    }

    console.log(`🔍 Testing custom content API for buyer: ${buyer.firstName} ${buyer.lastName}`);
    console.log(`👤 Buyer ID: ${buyer._id}`);

    // Mock request and response objects
    const req = {
      user: { id: buyer._id.toString() },
      query: {}
    };

    let responseData = null;
    let responseStatus = null;

    const res = {
      status: (code) => {
        responseStatus = code;
        return {
          json: (data) => {
            responseData = data;
            return res;
          }
        };
      }
    };

    const next = (error) => {
      console.error('❌ API Error:', error);
    };

    console.log('\n🔍 Calling getBuyerCustomContent API...');
    
    // Call the API function
    await getBuyerCustomContent(req, res, next);

    console.log(`\n📊 API Response Status: ${responseStatus}`);
    console.log(`📦 API Response Data:`);
    console.log(JSON.stringify(responseData, null, 2));

    if (responseData?.success && responseData?.data) {
      console.log(`\n📋 Custom content returned by API: ${responseData.data.length}`);
      
      responseData.data.forEach((content, index) => {
        console.log(`\n${index + 1}. Custom Content:`);
        console.log(`   - ID: ${content._id}`);
        console.log(`   - Title: ${content.title}`);
        console.log(`   - Request Title: ${content.requestTitle}`);
        console.log(`   - Content Type: ${content.contentType}`);
        console.log(`   - Completed At: ${content.completedAt}`);
        console.log(`   - File URL: ${content.fileUrl ? 'Present' : 'Missing'}`);
      });
    }

  } catch (error) {
    console.error('💥 Error testing custom content API:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('\n📡 MongoDB connection closed');
  }
};

// Run the test
testCustomContentAPI();
