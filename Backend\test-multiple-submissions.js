const mongoose = require('mongoose');
const CustomRequest = require('./models/CustomRequest');
const Content = require('./models/Content');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/xosportshub', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testMultipleSubmissions() {
  try {
    console.log('🔍 Testing multiple content submissions...');

    // Find the existing custom request
    const requestId = '689598e14edf319a861aec52';
    const request = await CustomRequest.findById(requestId);

    if (!request) {
      console.log('❌ Custom request not found');
      return;
    }

    console.log('📋 Current request state:');
    console.log('- Title:', request.title);
    console.log('- Status:', request.status);
    console.log('- Main content submission:', request.contentSubmission?.contentId);
    console.log('- Content submissions array length:', request.contentSubmissions?.length || 0);

    // Add a new content submission to the array
    const newSubmission = {
      submittedAt: new Date(),
      contentId: '6895910a04c0692741e6d319', // Using the same content ID for testing
      submissionMessage: 'This is a second content submission - should appear as separate download',
      isActive: true
    };

    // Initialize contentSubmissions array if it doesn't exist
    if (!request.contentSubmissions) {
      request.contentSubmissions = [];
    }

    // Add the new submission to the array
    request.contentSubmissions.push(newSubmission);

    // Save the request
    await request.save();

    console.log('✅ Added new content submission');
    console.log('📊 Updated state:');
    console.log('- Content submissions array length:', request.contentSubmissions.length);
    console.log('- New submission:', newSubmission);

    // Now test the getBuyerCustomContent logic
    console.log('\n🧪 Testing getBuyerCustomContent logic...');

    // Simulate the logic from getBuyerCustomContent
    const completedRequests = await CustomRequest.find({
      buyer: '689059afd71b572184c9f731',
      status: 'Completed',
      'contentSubmission.isSubmitted': true,
      $or: [
        {
          'sellerResponse.paymentType': 'full',
          'paymentDetails.initialPaymentCompleted': true
        },
        {
          'sellerResponse.paymentType': 'half',
          'paymentDetails.initialPaymentCompleted': true,
          'paymentDetails.finalPaymentCompleted': true
        }
      ]
    }).populate('contentSubmission.contentId')
      .populate('contentSubmissions.contentId')
      .populate('relatedContent');

    console.log('📋 Found', completedRequests.length, 'completed requests');

    // Extract content using the new logic
    const customContent = [];

    for (const request of completedRequests) {
      console.log(`\n📝 Processing request ${request._id}: title="${request.title}"`);

      // Get all content submissions for this request
      const submissions = request.contentSubmissions || [];
      const hasMainContent = !!request.contentSubmission?.contentId;

      console.log(`📊 Request has ${submissions.length} submissions and main content: ${hasMainContent}`);

      // Add all submissions from the contentSubmissions array
      for (const submission of submissions) {
        if (submission.contentId && submission.isActive) {
          const content = submission.contentId.toObject();
          console.log(`➕ Adding submission content: ${content._id}, title: "${content.title}"`);

          customContent.push({
            ...content,
            customRequestId: request._id,
            requestTitle: request.title,
            completedAt: submission.submittedAt,
            submissionMessage: submission.submissionMessage,
            submissionType: 'array'
          });
        }
      }

      // Also add the main content submission if it exists and is not already in the array
      if (hasMainContent) {
        const mainContent = request.contentSubmission.contentId.toObject();
        const alreadyAdded = submissions.some(sub =>
          sub.contentId && sub.contentId._id.toString() === mainContent._id.toString()
        );

        if (!alreadyAdded) {
          console.log(`➕ Adding main content: ${mainContent._id}, title: "${mainContent.title}"`);

          customContent.push({
            ...mainContent,
            customRequestId: request._id,
            requestTitle: request.title,
            completedAt: request.contentSubmission.submittedAt,
            submissionMessage: request.contentSubmission.submissionMessage,
            submissionType: 'main'
          });
        } else {
          console.log(`⚠️ Main content already in submissions array, skipping`);
        }
      }
    }

    console.log(`\n🎯 Final result: ${customContent.length} custom content items`);
    customContent.forEach((content, index) => {
      console.log(`${index + 1}. ${content.title} (${content.submissionType}) - ${content.submissionMessage}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

testMultipleSubmissions();
